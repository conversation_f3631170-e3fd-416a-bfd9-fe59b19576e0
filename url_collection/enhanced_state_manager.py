"""
增强状态管理器
支持精确断点续传、实时验证和数据完整性检查
"""

import json
import time
import os
import sqlite3
import threading
from datetime import datetime
from typing import Dict, List, Set, Any, Optional
from pathlib import Path

class EnhancedStateManager:
    """增强状态管理器 - 支持精确断点续传和数据完整性"""
    
    def __init__(self, session_name: str = None):
        if session_name is None:
            session_name = f"collection_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        self.session_name = session_name
        self.state_file = f"collection_state_{session_name}.json"
        self.urls_file = f"collected_urls_{session_name}.txt"
        self.db_file = f"collection_state_{session_name}.db"
        
        # 线程锁
        self._lock = threading.RLock()
        
        # 初始化数据库
        self._init_database()
        
        # 状态数据
        self.state = {
            "session_name": session_name,
            "start_time": time.time(),
            "last_update": time.time(),
            "total_searches": 0,
            "completed_searches": 0,
            "collected_urls": set(),
            "validated_urls": set(),
            "invalid_urls": set(),
            "search_progress": {},
            "failed_searches": [],
            "statistics": {
                "total_pages_processed": 0,
                "total_urls_found": 0,
                "total_urls_validated": 0,
                "validation_success_rate": 0.0
            }
        }
        
        # 尝试加载现有状态
        self.load_state()
    
    def _init_database(self):
        """初始化SQLite数据库"""
        try:
            with sqlite3.connect(self.db_file) as conn:
                cursor = conn.cursor()
                
                # 创建搜索进度表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS search_progress (
                        search_key TEXT PRIMARY KEY,
                        config TEXT,
                        current_page INTEGER DEFAULT 1,
                        total_pages INTEGER DEFAULT -1,
                        status TEXT DEFAULT 'pending',
                        last_update REAL,
                        error_count INTEGER DEFAULT 0,
                        last_error TEXT
                    )
                ''')
                
                # 创建URL表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS urls (
                        url TEXT PRIMARY KEY,
                        search_key TEXT,
                        found_time REAL,
                        validation_status TEXT DEFAULT 'pending',
                        validation_time REAL,
                        is_valid BOOLEAN
                    )
                ''')
                
                # 创建会话信息表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS session_info (
                        key TEXT PRIMARY KEY,
                        value TEXT
                    )
                ''')
                
                conn.commit()
                print(f"✅ 数据库初始化完成: {self.db_file}")
                
        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
            raise
    
    def save_search_progress(self, search_key: str, config: Dict, current_page: int, 
                           status: str = 'in_progress', error: str = None):
        """保存搜索进度到数据库"""
        with self._lock:
            try:
                with sqlite3.connect(self.db_file) as conn:
                    cursor = conn.cursor()
                    
                    # 获取当前错误计数
                    cursor.execute(
                        'SELECT error_count FROM search_progress WHERE search_key = ?',
                        (search_key,)
                    )
                    result = cursor.fetchone()
                    error_count = (result[0] if result else 0) + (1 if error else 0)
                    
                    cursor.execute('''
                        INSERT OR REPLACE INTO search_progress 
                        (search_key, config, current_page, status, last_update, error_count, last_error)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        search_key,
                        json.dumps(config),
                        current_page,
                        status,
                        time.time(),
                        error_count,
                        error
                    ))
                    
                    conn.commit()
                    
            except Exception as e:
                print(f"❌ 保存搜索进度失败: {e}")
    
    def get_search_resume_point(self, search_key: str) -> int:
        """获取搜索的断点续传位置"""
        with self._lock:
            try:
                with sqlite3.connect(self.db_file) as conn:
                    cursor = conn.cursor()
                    cursor.execute(
                        'SELECT current_page, status FROM search_progress WHERE search_key = ?',
                        (search_key,)
                    )
                    result = cursor.fetchone()
                    
                    if result:
                        current_page, status = result
                        if status == 'completed':
                            return -1  # 已完成
                        return current_page
                    
                    return 1  # 从第一页开始
                    
            except Exception as e:
                print(f"❌ 获取断点续传位置失败: {e}")
                return 1
    
    def is_search_completed(self, search_key: str) -> bool:
        """检查搜索是否已完成"""
        with self._lock:
            try:
                with sqlite3.connect(self.db_file) as conn:
                    cursor = conn.cursor()
                    cursor.execute(
                        'SELECT status FROM search_progress WHERE search_key = ?',
                        (search_key,)
                    )
                    result = cursor.fetchone()
                    return result and result[0] == 'completed'
                    
            except Exception as e:
                print(f"❌ 检查搜索状态失败: {e}")
                return False
    
    def add_urls(self, urls: List[str], search_key: str) -> List[str]:
        """添加URL并返回新增的URL"""
        with self._lock:
            new_urls = []
            current_time = time.time()
            
            try:
                with sqlite3.connect(self.db_file) as conn:
                    cursor = conn.cursor()
                    
                    for url in urls:
                        # 检查URL是否已存在
                        cursor.execute('SELECT url FROM urls WHERE url = ?', (url,))
                        if not cursor.fetchone():
                            cursor.execute('''
                                INSERT INTO urls (url, search_key, found_time)
                                VALUES (?, ?, ?)
                            ''', (url, search_key, current_time))
                            new_urls.append(url)
                            self.state["collected_urls"].add(url)
                    
                    conn.commit()
                    
                # 更新统计
                self.state["statistics"]["total_urls_found"] += len(new_urls)
                self.save_state()
                
                return new_urls
                
            except Exception as e:
                print(f"❌ 添加URL失败: {e}")
                return []
    
    def mark_url_validated(self, url: str, is_valid: bool):
        """标记URL验证结果"""
        with self._lock:
            try:
                with sqlite3.connect(self.db_file) as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        UPDATE urls 
                        SET validation_status = ?, validation_time = ?, is_valid = ?
                        WHERE url = ?
                    ''', (
                        'completed',
                        time.time(),
                        is_valid,
                        url
                    ))
                    conn.commit()
                
                # 更新内存状态
                if is_valid:
                    self.state["validated_urls"].add(url)
                else:
                    self.state["invalid_urls"].add(url)
                
                # 更新统计
                self.state["statistics"]["total_urls_validated"] += 1
                total_validated = self.state["statistics"]["total_urls_validated"]
                valid_count = len(self.state["validated_urls"])
                self.state["statistics"]["validation_success_rate"] = (
                    (valid_count / total_validated * 100) if total_validated > 0 else 0
                )
                
            except Exception as e:
                print(f"❌ 标记URL验证结果失败: {e}")
    
    def get_pending_validation_urls(self, limit: int = 100) -> List[str]:
        """获取待验证的URL"""
        with self._lock:
            try:
                with sqlite3.connect(self.db_file) as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        SELECT url FROM urls 
                        WHERE validation_status = 'pending'
                        LIMIT ?
                    ''', (limit,))
                    return [row[0] for row in cursor.fetchall()]
                    
            except Exception as e:
                print(f"❌ 获取待验证URL失败: {e}")
                return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取详细统计信息"""
        with self._lock:
            try:
                with sqlite3.connect(self.db_file) as conn:
                    cursor = conn.cursor()
                    
                    # 获取URL统计
                    cursor.execute('SELECT COUNT(*) FROM urls')
                    total_urls = cursor.fetchone()[0]
                    
                    cursor.execute('SELECT COUNT(*) FROM urls WHERE is_valid = 1')
                    valid_urls = cursor.fetchone()[0]
                    
                    cursor.execute('SELECT COUNT(*) FROM urls WHERE validation_status = "completed"')
                    validated_urls = cursor.fetchone()[0]
                    
                    # 获取搜索统计
                    cursor.execute('SELECT COUNT(*) FROM search_progress')
                    total_searches = cursor.fetchone()[0]
                    
                    cursor.execute('SELECT COUNT(*) FROM search_progress WHERE status = "completed"')
                    completed_searches = cursor.fetchone()[0]
                    
                    return {
                        "session_name": self.session_name,
                        "total_urls": total_urls,
                        "valid_urls": valid_urls,
                        "validated_urls": validated_urls,
                        "validation_success_rate": (valid_urls / validated_urls * 100) if validated_urls > 0 else 0,
                        "total_searches": total_searches,
                        "completed_searches": completed_searches,
                        "search_progress": (completed_searches / total_searches * 100) if total_searches > 0 else 0,
                        "runtime": time.time() - self.state["start_time"]
                    }
                    
            except Exception as e:
                print(f"❌ 获取统计信息失败: {e}")
                return {}
    
    def save_state(self):
        """保存状态到JSON文件"""
        try:
            state_copy = self.state.copy()
            state_copy["collected_urls"] = list(self.state["collected_urls"])
            state_copy["validated_urls"] = list(self.state["validated_urls"])
            state_copy["invalid_urls"] = list(self.state["invalid_urls"])
            state_copy["last_update"] = time.time()
            
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state_copy, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"❌ 保存状态失败: {e}")
    
    def load_state(self):
        """从文件加载状态"""
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    loaded_state = json.load(f)
                
                # 恢复set类型
                loaded_state["collected_urls"] = set(loaded_state.get("collected_urls", []))
                loaded_state["validated_urls"] = set(loaded_state.get("validated_urls", []))
                loaded_state["invalid_urls"] = set(loaded_state.get("invalid_urls", []))
                
                self.state.update(loaded_state)
                print(f"✅ 状态加载成功: {self.state_file}")
                
        except Exception as e:
            print(f"❌ 状态加载失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        try:
            self.save_state()
            print(f"✅ 状态管理器清理完成")
        except Exception as e:
            print(f"❌ 清理失败: {e}")
