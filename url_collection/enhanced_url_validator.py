"""
增强URL验证器
支持实时网络验证、缓存和批量处理
"""

import asyncio
import aiohttp
import time
import json
import re
from typing import List, Dict, Set, Any, Optional
from urllib.parse import urlparse
import hashlib
from pathlib import Path

class EnhancedURLValidator:
    """增强URL验证器 - 支持实时验证和智能缓存"""
    
    def __init__(self, cache_file: str = "url_validation_cache.json"):
        self.cache_file = cache_file
        self.validation_cache = {}
        self.session = None
        
        # 验证配置
        self.config = {
            "timeout": 10,
            "max_retries": 2,
            "retry_delay": 1,
            "batch_size": 20,
            "concurrent_limit": 5,
            "cache_expire_hours": 24
        }
        
        # 加载缓存
        self.load_cache()
        
        # 统计信息
        self.stats = {
            "total_validated": 0,
            "cache_hits": 0,
            "network_requests": 0,
            "validation_errors": 0
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        connector = aiohttp.TCPConnector(
            limit=self.config["concurrent_limit"],
            limit_per_host=3,
            ttl_dns_cache=300,
            use_dns_cache=True
        )
        
        timeout = aiohttp.ClientTimeout(total=self.config["timeout"])
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        if self.session:
            await self.session.close()
        self.save_cache()
    
    def is_valid_format(self, url: str) -> bool:
        """验证URL格式"""
        try:
            # BOSS直聘职位URL格式验证
            patterns = [
                r'^https://www\.zhipin\.com/job_detail/[a-zA-Z0-9]+\.html',
                r'^https://www\.zhipin\.com/job_detail/[a-zA-Z0-9]+\.html\?',
            ]
            
            for pattern in patterns:
                if re.match(pattern, url):
                    return True
            
            return False
            
        except Exception:
            return False
    
    def _get_cache_key(self, url: str) -> str:
        """生成缓存键"""
        return hashlib.md5(url.encode()).hexdigest()
    
    def _is_cache_valid(self, cache_entry: Dict) -> bool:
        """检查缓存是否有效"""
        if not cache_entry:
            return False
        
        cache_time = cache_entry.get("timestamp", 0)
        expire_time = self.config["cache_expire_hours"] * 3600
        
        return (time.time() - cache_time) < expire_time
    
    def get_cached_result(self, url: str) -> Optional[bool]:
        """获取缓存的验证结果"""
        cache_key = self._get_cache_key(url)
        cache_entry = self.validation_cache.get(cache_key)
        
        if self._is_cache_valid(cache_entry):
            self.stats["cache_hits"] += 1
            return cache_entry["is_valid"]
        
        return None
    
    def cache_result(self, url: str, is_valid: bool, response_code: int = None):
        """缓存验证结果"""
        cache_key = self._get_cache_key(url)
        self.validation_cache[cache_key] = {
            "url": url,
            "is_valid": is_valid,
            "response_code": response_code,
            "timestamp": time.time()
        }
    
    async def validate_url_network(self, url: str) -> Dict[str, Any]:
        """网络验证URL可访问性"""
        if not self.session:
            raise RuntimeError("Session not initialized. Use async context manager.")
        
        result = {
            "url": url,
            "is_valid": False,
            "response_code": None,
            "error": None,
            "response_time": None
        }
        
        start_time = time.time()
        
        for attempt in range(self.config["max_retries"] + 1):
            try:
                async with self.session.head(url, allow_redirects=True) as response:
                    result["response_code"] = response.status
                    result["response_time"] = time.time() - start_time
                    
                    # 检查响应状态
                    if response.status == 200:
                        result["is_valid"] = True
                    elif response.status in [301, 302, 303, 307, 308]:
                        # 重定向也认为是有效的
                        result["is_valid"] = True
                    elif response.status == 404:
                        result["is_valid"] = False
                        result["error"] = "页面不存在"
                    elif response.status == 403:
                        result["is_valid"] = False
                        result["error"] = "访问被拒绝"
                    else:
                        result["is_valid"] = False
                        result["error"] = f"HTTP {response.status}"
                    
                    break
                    
            except asyncio.TimeoutError:
                result["error"] = "请求超时"
                if attempt < self.config["max_retries"]:
                    await asyncio.sleep(self.config["retry_delay"])
                    continue
                    
            except aiohttp.ClientError as e:
                result["error"] = f"网络错误: {str(e)}"
                if attempt < self.config["max_retries"]:
                    await asyncio.sleep(self.config["retry_delay"])
                    continue
                    
            except Exception as e:
                result["error"] = f"未知错误: {str(e)}"
                break
        
        self.stats["network_requests"] += 1
        if result["error"]:
            self.stats["validation_errors"] += 1
        
        return result
    
    async def validate_single_url(self, url: str) -> Dict[str, Any]:
        """验证单个URL（包含格式和网络验证）"""
        self.stats["total_validated"] += 1
        
        # 格式验证
        if not self.is_valid_format(url):
            return {
                "url": url,
                "is_valid": False,
                "error": "URL格式无效",
                "source": "format_check"
            }
        
        # 检查缓存
        cached_result = self.get_cached_result(url)
        if cached_result is not None:
            return {
                "url": url,
                "is_valid": cached_result,
                "source": "cache"
            }
        
        # 网络验证
        network_result = await self.validate_url_network(url)
        
        # 缓存结果
        self.cache_result(
            url, 
            network_result["is_valid"], 
            network_result["response_code"]
        )
        
        network_result["source"] = "network"
        return network_result
    
    async def batch_validate_urls(self, urls: List[str]) -> Dict[str, List[str]]:
        """批量验证URL"""
        print(f"🔍 开始批量验证 {len(urls)} 个URL...")
        
        valid_urls = []
        invalid_urls = []
        errors = []
        
        # 分批处理
        batch_size = self.config["batch_size"]
        semaphore = asyncio.Semaphore(self.config["concurrent_limit"])
        
        async def validate_with_semaphore(url):
            async with semaphore:
                try:
                    result = await self.validate_single_url(url)
                    return result
                except Exception as e:
                    return {
                        "url": url,
                        "is_valid": False,
                        "error": str(e),
                        "source": "exception"
                    }
        
        # 处理所有URL
        for i in range(0, len(urls), batch_size):
            batch = urls[i:i + batch_size]
            print(f"📊 验证进度: {i + 1}-{min(i + batch_size, len(urls))}/{len(urls)}")
            
            # 并发验证当前批次
            tasks = [validate_with_semaphore(url) for url in batch]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            for result in results:
                if isinstance(result, Exception):
                    errors.append(str(result))
                    continue
                
                if result["is_valid"]:
                    valid_urls.append(result["url"])
                else:
                    invalid_urls.append(result["url"])
                    if result.get("error"):
                        errors.append(f"{result['url']}: {result['error']}")
            
            # 批次间延时
            if i + batch_size < len(urls):
                await asyncio.sleep(0.5)
        
        # 保存缓存
        self.save_cache()
        
        print(f"✅ 验证完成: {len(valid_urls)} 有效, {len(invalid_urls)} 无效")
        
        return {
            "valid": valid_urls,
            "invalid": invalid_urls,
            "errors": errors,
            "statistics": self.get_statistics()
        }
    
    def deduplicate_urls(self, urls: List[str]) -> List[str]:
        """去重URL列表"""
        seen = set()
        unique_urls = []
        
        for url in urls:
            # 标准化URL（移除查询参数等）
            normalized_url = self._normalize_url(url)
            if normalized_url not in seen:
                seen.add(normalized_url)
                unique_urls.append(url)
        
        print(f"🔄 去重完成: {len(urls)} -> {len(unique_urls)}")
        return unique_urls
    
    def _normalize_url(self, url: str) -> str:
        """标准化URL用于去重"""
        try:
            parsed = urlparse(url)
            # 移除查询参数和片段
            normalized = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
            return normalized.lower()
        except:
            return url.lower()
    
    def load_cache(self):
        """加载验证缓存"""
        try:
            if Path(self.cache_file).exists():
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    self.validation_cache = json.load(f)
                print(f"✅ 验证缓存加载成功: {len(self.validation_cache)} 条记录")
        except Exception as e:
            print(f"❌ 验证缓存加载失败: {e}")
            self.validation_cache = {}
    
    def save_cache(self):
        """保存验证缓存"""
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.validation_cache, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"❌ 验证缓存保存失败: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取验证统计信息"""
        cache_hit_rate = (
            (self.stats["cache_hits"] / self.stats["total_validated"] * 100)
            if self.stats["total_validated"] > 0 else 0
        )
        
        error_rate = (
            (self.stats["validation_errors"] / self.stats["network_requests"] * 100)
            if self.stats["network_requests"] > 0 else 0
        )
        
        return {
            "total_validated": self.stats["total_validated"],
            "cache_hits": self.stats["cache_hits"],
            "network_requests": self.stats["network_requests"],
            "validation_errors": self.stats["validation_errors"],
            "cache_hit_rate": cache_hit_rate,
            "error_rate": error_rate,
            "cache_size": len(self.validation_cache)
        }
    
    def clear_cache(self):
        """清空验证缓存"""
        self.validation_cache = {}
        try:
            Path(self.cache_file).unlink(missing_ok=True)
            print("✅ 验证缓存已清空")
        except Exception as e:
            print(f"❌ 清空缓存失败: {e}")
