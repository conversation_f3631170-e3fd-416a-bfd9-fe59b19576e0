"""
高效去重模块
支持布隆过滤器和精确去重的组合策略
"""

import hashlib
import json
import pickle
from typing import List, Set, Dict, Any
from pathlib import Path
import time

try:
    from pybloom_live import BloomFilter
    BLOOM_AVAILABLE = True
except ImportError:
    BLOOM_AVAILABLE = False
    print("⚠️  pybloom_live未安装，将使用精确去重")

class Deduplicator:
    """高效去重器 - 支持布隆过滤器和精确去重"""
    
    def __init__(self, session_name: str = None, capacity: int = 1000000, error_rate: float = 0.001):
        self.session_name = session_name or f"dedup_{int(time.time())}"
        self.capacity = capacity
        self.error_rate = error_rate
        
        # 文件路径
        self.bloom_file = f"bloom_filter_{self.session_name}.pkl"
        self.exact_file = f"exact_dedup_{self.session_name}.json"
        self.stats_file = f"dedup_stats_{self.session_name}.json"
        
        # 初始化布隆过滤器
        self.bloom_filter = None
        if BLOOM_AVAILABLE:
            self._init_bloom_filter()
        
        # 精确去重集合
        self.exact_set = set()
        
        # 统计信息
        self.stats = {
            "total_checked": 0,
            "duplicates_found": 0,
            "bloom_hits": 0,
            "exact_hits": 0,
            "false_positives": 0,
            "session_start": time.time()
        }
        
        # 加载现有数据
        self.load_state()
    
    def _init_bloom_filter(self):
        """初始化布隆过滤器"""
        try:
            if Path(self.bloom_file).exists():
                with open(self.bloom_file, 'rb') as f:
                    self.bloom_filter = pickle.load(f)
                print(f"✅ 布隆过滤器加载成功: {self.bloom_file}")
            else:
                self.bloom_filter = BloomFilter(capacity=self.capacity, error_rate=self.error_rate)
                print(f"✅ 新建布隆过滤器: 容量={self.capacity}, 错误率={self.error_rate}")
        except Exception as e:
            print(f"❌ 布隆过滤器初始化失败: {e}")
            self.bloom_filter = None
    
    def _normalize_url(self, url: str) -> str:
        """标准化URL用于去重"""
        try:
            # 移除常见的无关参数
            url = url.split('?')[0]  # 移除查询参数
            url = url.split('#')[0]  # 移除锚点
            url = url.rstrip('/')    # 移除尾部斜杠
            return url.lower().strip()
        except:
            return url.lower().strip()
    
    def _get_url_hash(self, url: str) -> str:
        """获取URL的哈希值"""
        normalized_url = self._normalize_url(url)
        return hashlib.md5(normalized_url.encode()).hexdigest()
    
    def is_duplicate(self, url: str) -> bool:
        """检查URL是否重复"""
        self.stats["total_checked"] += 1
        
        normalized_url = self._normalize_url(url)
        url_hash = self._get_url_hash(url)
        
        # 首先检查精确集合
        if url_hash in self.exact_set:
            self.stats["duplicates_found"] += 1
            self.stats["exact_hits"] += 1
            return True
        
        # 如果有布隆过滤器，先检查布隆过滤器
        if self.bloom_filter and BLOOM_AVAILABLE:
            if normalized_url in self.bloom_filter:
                self.stats["bloom_hits"] += 1
                
                # 布隆过滤器可能有假阳性，需要精确验证
                if url_hash not in self.exact_set:
                    # 这是假阳性，添加到精确集合
                    self.exact_set.add(url_hash)
                    self.stats["false_positives"] += 1
                    return False
                else:
                    self.stats["duplicates_found"] += 1
                    return True
        
        return False
    
    def add_url(self, url: str) -> bool:
        """添加URL到去重集合，返回是否为新URL"""
        if self.is_duplicate(url):
            return False
        
        normalized_url = self._normalize_url(url)
        url_hash = self._get_url_hash(url)
        
        # 添加到精确集合
        self.exact_set.add(url_hash)
        
        # 添加到布隆过滤器
        if self.bloom_filter and BLOOM_AVAILABLE:
            self.bloom_filter.add(normalized_url)
        
        return True
    
    def batch_deduplicate(self, urls: List[str]) -> List[str]:
        """批量去重URL列表"""
        print(f"🔄 开始批量去重: {len(urls)} 个URL")
        start_time = time.time()
        
        unique_urls = []
        duplicate_count = 0
        
        for i, url in enumerate(urls):
            if self.add_url(url):
                unique_urls.append(url)
            else:
                duplicate_count += 1
            
            # 进度显示
            if (i + 1) % 1000 == 0:
                print(f"📊 去重进度: {i + 1}/{len(urls)}, 去重: {duplicate_count}")
        
        processing_time = time.time() - start_time
        
        print(f"✅ 去重完成: {len(urls)} -> {len(unique_urls)} (耗时: {processing_time:.2f}s)")
        print(f"📊 重复URL: {duplicate_count} 个")
        
        # 定期保存状态
        if len(unique_urls) % 100 == 0:
            self.save_state()
        
        return unique_urls
    
    def get_duplicate_urls(self, urls: List[str]) -> List[str]:
        """获取重复的URL列表"""
        duplicates = []
        for url in urls:
            if self.is_duplicate(url):
                duplicates.append(url)
        return duplicates
    
    def get_unique_urls(self, urls: List[str]) -> List[str]:
        """获取唯一的URL列表（不修改内部状态）"""
        seen = set()
        unique_urls = []
        
        for url in urls:
            url_hash = self._get_url_hash(url)
            if url_hash not in seen:
                seen.add(url_hash)
                unique_urls.append(url)
        
        return unique_urls
    
    def merge_with_other(self, other_deduplicator: 'Deduplicator'):
        """合并另一个去重器的数据"""
        print(f"🔄 合并去重器数据...")
        
        # 合并精确集合
        before_count = len(self.exact_set)
        self.exact_set.update(other_deduplicator.exact_set)
        after_count = len(self.exact_set)
        
        print(f"📊 合并完成: {before_count} + {len(other_deduplicator.exact_set)} -> {after_count}")
        
        # 如果都有布隆过滤器，需要重建
        if (self.bloom_filter and other_deduplicator.bloom_filter and 
            BLOOM_AVAILABLE):
            print("🔄 重建布隆过滤器...")
            self._rebuild_bloom_filter()
    
    def _rebuild_bloom_filter(self):
        """重建布隆过滤器"""
        if not BLOOM_AVAILABLE:
            return
        
        try:
            # 创建新的布隆过滤器
            new_capacity = max(self.capacity, len(self.exact_set) * 2)
            self.bloom_filter = BloomFilter(capacity=new_capacity, error_rate=self.error_rate)
            
            # 重新添加所有URL哈希
            for url_hash in self.exact_set:
                self.bloom_filter.add(url_hash)
            
            print(f"✅ 布隆过滤器重建完成: 容量={new_capacity}")
            
        except Exception as e:
            print(f"❌ 布隆过滤器重建失败: {e}")
    
    def save_state(self):
        """保存去重状态"""
        try:
            # 保存布隆过滤器
            if self.bloom_filter and BLOOM_AVAILABLE:
                with open(self.bloom_file, 'wb') as f:
                    pickle.dump(self.bloom_filter, f)
            
            # 保存精确集合
            with open(self.exact_file, 'w', encoding='utf-8') as f:
                json.dump(list(self.exact_set), f, ensure_ascii=False, indent=2)
            
            # 保存统计信息
            self.stats["last_save"] = time.time()
            with open(self.stats_file, 'w', encoding='utf-8') as f:
                json.dump(self.stats, f, ensure_ascii=False, indent=2)
            
        except Exception as e:
            print(f"❌ 保存去重状态失败: {e}")
    
    def load_state(self):
        """加载去重状态"""
        try:
            # 加载精确集合
            if Path(self.exact_file).exists():
                with open(self.exact_file, 'r', encoding='utf-8') as f:
                    url_list = json.load(f)
                    self.exact_set = set(url_list)
                print(f"✅ 精确去重集合加载成功: {len(self.exact_set)} 个URL")
            
            # 加载统计信息
            if Path(self.stats_file).exists():
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    saved_stats = json.load(f)
                    self.stats.update(saved_stats)
                print(f"✅ 去重统计信息加载成功")
            
        except Exception as e:
            print(f"❌ 加载去重状态失败: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取去重统计信息"""
        runtime = time.time() - self.stats["session_start"]
        
        duplicate_rate = (
            (self.stats["duplicates_found"] / self.stats["total_checked"] * 100)
            if self.stats["total_checked"] > 0 else 0
        )
        
        bloom_efficiency = (
            (self.stats["bloom_hits"] / self.stats["total_checked"] * 100)
            if self.stats["total_checked"] > 0 else 0
        )
        
        return {
            "session_name": self.session_name,
            "total_checked": self.stats["total_checked"],
            "duplicates_found": self.stats["duplicates_found"],
            "unique_urls": len(self.exact_set),
            "duplicate_rate": duplicate_rate,
            "bloom_hits": self.stats["bloom_hits"],
            "exact_hits": self.stats["exact_hits"],
            "false_positives": self.stats["false_positives"],
            "bloom_efficiency": bloom_efficiency,
            "runtime": runtime,
            "bloom_available": BLOOM_AVAILABLE
        }
    
    def print_statistics(self):
        """打印去重统计信息"""
        stats = self.get_statistics()
        
        print(f"\n📊 去重统计信息")
        print(f"=" * 50)
        print(f"🔍 总检查数: {stats['total_checked']}")
        print(f"🔄 重复发现: {stats['duplicates_found']}")
        print(f"✨ 唯一URL: {stats['unique_urls']}")
        print(f"📈 重复率: {stats['duplicate_rate']:.2f}%")
        if BLOOM_AVAILABLE:
            print(f"🌸 布隆命中: {stats['bloom_hits']}")
            print(f"🎯 精确命中: {stats['exact_hits']}")
            print(f"⚠️  假阳性: {stats['false_positives']}")
            print(f"⚡ 布隆效率: {stats['bloom_efficiency']:.2f}%")
        print(f"⏱️  运行时间: {stats['runtime']:.2f}s")
        print(f"=" * 50)
    
    def clear_all(self):
        """清空所有去重数据"""
        self.exact_set.clear()
        
        if self.bloom_filter and BLOOM_AVAILABLE:
            self.bloom_filter = BloomFilter(capacity=self.capacity, error_rate=self.error_rate)
        
        # 重置统计
        self.stats = {
            "total_checked": 0,
            "duplicates_found": 0,
            "bloom_hits": 0,
            "exact_hits": 0,
            "false_positives": 0,
            "session_start": time.time()
        }
        
        # 删除文件
        for file_path in [self.bloom_file, self.exact_file, self.stats_file]:
            try:
                Path(file_path).unlink(missing_ok=True)
            except:
                pass
        
        print("✅ 去重数据已清空")
