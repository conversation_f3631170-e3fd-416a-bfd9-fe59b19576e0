"""
智能控制器
统一管理后台爬虫的所有功能
"""

import asyncio
import threading
import time
import json
from typing import Dict, List, Any, Callable, Optional
from datetime import datetime
import signal
import sys

from .enhanced_state_manager import EnhancedStateManager
from .enhanced_url_validator import EnhancedURLValidator
from .deduplicator import Deduplicator
from .url_collector import URLCollector
from .search_config import SearchConfig

class SmartController:
    """智能控制器 - 统一管理后台爬虫功能"""
    
    def __init__(self, session_name: str = None):
        self.session_name = session_name or f"smart_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 核心组件
        self.state_manager = EnhancedStateManager(self.session_name)
        self.deduplicator = Deduplicator(self.session_name)
        self.url_collector = URLCollector()
        self.search_config = SearchConfig()
        
        # 控制状态
        self.is_running = False
        self.is_paused = False
        self.stop_event = threading.Event()
        
        # 工作线程
        self.collection_thread = None
        self.validation_thread = None
        self.monitor_thread = None
        
        # 队列和缓冲
        self.validation_queue = asyncio.Queue()
        self.result_buffer = []
        
        # 配置
        self.config = {
            "max_concurrent_searches": 3,
            "max_concurrent_validations": 5,
            "validation_batch_size": 50,
            "save_interval": 30,
            "progress_report_interval": 10,
            "max_pages_per_search": 20,
            "enable_real_time_validation": True,
            "enable_deduplication": True,
            "headless_mode": True
        }
        
        # 回调函数
        self.callbacks = {
            "progress": None,
            "url_found": None,
            "validation_complete": None,
            "error": None
        }
        
        # 统计信息
        self.stats = {
            "start_time": 0,
            "total_searches_planned": 0,
            "searches_completed": 0,
            "urls_collected": 0,
            "urls_validated": 0,
            "current_activity": "待机中"
        }
        
        # 注册信号处理
        self._setup_signal_handlers()
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            print(f"\n🛑 接收到信号 {signum}，正在优雅停止...")
            self.stop()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def set_config(self, **kwargs):
        """设置配置参数"""
        for key, value in kwargs.items():
            if key in self.config:
                self.config[key] = value
                print(f"✅ 配置更新: {key} = {value}")
            else:
                print(f"⚠️  未知配置项: {key}")
    
    def set_callback(self, event: str, callback: Callable):
        """设置回调函数"""
        if event in self.callbacks:
            self.callbacks[event] = callback
            print(f"✅ 回调设置: {event}")
        else:
            print(f"⚠️  未知回调事件: {event}")
    
    async def start_collection(self, search_type: str = "comprehensive", **kwargs) -> str:
        """开始收集任务"""
        if self.is_running:
            return "❌ 收集器已在运行中"
        
        try:
            print(f"🚀 启动智能收集器 - 会话: {self.session_name}")
            print(f"📋 收集类型: {search_type}")
            
            # 获取搜索配置
            search_configs = self._get_search_configs(search_type, **kwargs)
            if not search_configs:
                return "❌ 没有可用的搜索配置"
            
            self.stats["total_searches_planned"] = len(search_configs)
            self.stats["start_time"] = time.time()
            
            # 过滤已完成的搜索（断点续传）
            remaining_configs = self._filter_completed_searches(search_configs)
            
            if not remaining_configs:
                return "✅ 所有搜索任务已完成"
            
            # 启动收集
            self.is_running = True
            self.stop_event.clear()
            
            # 启动工作线程
            self.collection_thread = threading.Thread(
                target=self._run_collection_worker, 
                args=(remaining_configs,), 
                daemon=True
            )
            
            if self.config["enable_real_time_validation"]:
                self.validation_thread = threading.Thread(
                    target=self._run_validation_worker, 
                    daemon=True
                )
                self.validation_thread.start()
            
            self.monitor_thread = threading.Thread(
                target=self._run_monitor_worker, 
                daemon=True
            )
            
            self.collection_thread.start()
            self.monitor_thread.start()
            
            return f"✅ 收集已启动 - 剩余任务: {len(remaining_configs)}"
            
        except Exception as e:
            self.is_running = False
            return f"❌ 启动失败: {e}"
    
    def _get_search_configs(self, search_type: str, **kwargs) -> List[Dict]:
        """获取搜索配置"""
        if search_type == "comprehensive":
            return self.search_config.get_comprehensive_search_configs()
        elif search_type == "keywords":
            keywords = kwargs.get("keywords", ["Python", "Java"])
            cities = kwargs.get("cities", ["北京", "上海"])
            return self._build_keyword_configs(keywords, cities)
        elif search_type == "quick":
            max_urls = kwargs.get("max_urls", 1000)
            return self._build_quick_configs(max_urls)
        else:
            return []
    
    def _build_keyword_configs(self, keywords: List[str], cities: List[str]) -> List[Dict]:
        """构建关键词搜索配置"""
        configs = []
        for keyword in keywords:
            for city in cities:
                config = self.search_config.get_search_params(city=city, keyword=keyword)
                configs.append(config)
        return configs
    
    def _build_quick_configs(self, max_urls: int) -> List[Dict]:
        """构建快速收集配置"""
        hot_keywords = ["Python", "Java", "前端", "产品经理"]
        major_cities = ["北京", "上海", "广州", "深圳"]
        
        configs = []
        for keyword in hot_keywords[:2]:  # 限制关键词数量
            for city in major_cities[:2]:  # 限制城市数量
                config = self.search_config.get_search_params(city=city, keyword=keyword)
                configs.append(config)
        
        return configs
    
    def _filter_completed_searches(self, search_configs: List[Dict]) -> List[Dict]:
        """过滤已完成的搜索"""
        remaining = []
        for config in search_configs:
            search_key = self._get_search_key(config)
            if not self.state_manager.is_search_completed(search_key):
                remaining.append(config)
            else:
                print(f"⏭️  跳过已完成: {search_key}")
        
        print(f"📋 断点续传: {len(search_configs)} -> {len(remaining)} 个任务")
        return remaining
    
    def _get_search_key(self, config: Dict) -> str:
        """生成搜索键"""
        city = config.get('city', 'unknown')
        query = config.get('query', 'unknown')
        return f"{city}_{query}"
    
    def _run_collection_worker(self, search_configs: List[Dict]):
        """收集工作线程"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            loop.run_until_complete(self._async_collection_worker(search_configs))
        except Exception as e:
            print(f"❌ 收集工作线程异常: {e}")
            if self.callbacks["error"]:
                self.callbacks["error"](f"收集异常: {e}")
        finally:
            loop.close()
    
    async def _async_collection_worker(self, search_configs: List[Dict]):
        """异步收集工作器"""
        semaphore = asyncio.Semaphore(self.config["max_concurrent_searches"])
        
        async def process_search(config):
            async with semaphore:
                if self.stop_event.is_set():
                    return
                
                search_key = self._get_search_key(config)
                self.stats["current_activity"] = f"搜索: {search_key}"
                
                try:
                    # 获取断点续传位置
                    start_page = self.state_manager.get_search_resume_point(search_key)
                    if start_page == -1:
                        return  # 已完成
                    
                    print(f"🔍 开始搜索: {search_key} (从第{start_page}页)")
                    
                    # 执行搜索
                    urls = await self.url_collector.search_jobs_by_params(
                        config, 
                        max_pages=self.config["max_pages_per_search"],
                        start_page=start_page
                    )
                    
                    if urls:
                        # 去重处理
                        if self.config["enable_deduplication"]:
                            unique_urls = self.deduplicator.batch_deduplicate(urls)
                        else:
                            unique_urls = list(set(urls))
                        
                        # 添加到状态管理器
                        new_urls = self.state_manager.add_urls(unique_urls, search_key)
                        
                        # 添加到验证队列
                        if self.config["enable_real_time_validation"]:
                            for url in new_urls:
                                await self.validation_queue.put(url)
                        
                        # 更新统计
                        self.stats["urls_collected"] += len(new_urls)
                        self.stats["searches_completed"] += 1
                        
                        # 标记搜索完成
                        self.state_manager.save_search_progress(
                            search_key, config, -1, "completed"
                        )
                        
                        # 回调通知
                        if self.callbacks["url_found"]:
                            self.callbacks["url_found"](new_urls, search_key)
                        
                        print(f"✅ 搜索完成: {search_key} - 新增 {len(new_urls)} 个URL")
                    
                except Exception as e:
                    print(f"❌ 搜索失败: {search_key} - {e}")
                    self.state_manager.save_search_progress(
                        search_key, config, start_page, "failed", str(e)
                    )
        
        # 并发处理所有搜索
        tasks = [process_search(config) for config in search_configs]
        await asyncio.gather(*tasks, return_exceptions=True)
        
        print("🎉 所有搜索任务完成")
        self.stats["current_activity"] = "搜索完成"
    
    def _run_validation_worker(self):
        """验证工作线程"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            loop.run_until_complete(self._async_validation_worker())
        except Exception as e:
            print(f"❌ 验证工作线程异常: {e}")
        finally:
            loop.close()
    
    async def _async_validation_worker(self):
        """异步验证工作器"""
        async with EnhancedURLValidator() as validator:
            batch = []
            
            while self.is_running and not self.stop_event.is_set():
                try:
                    # 收集批次
                    try:
                        url = await asyncio.wait_for(
                            self.validation_queue.get(), 
                            timeout=1.0
                        )
                        batch.append(url)
                        
                        if len(batch) >= self.config["validation_batch_size"]:
                            await self._process_validation_batch(validator, batch)
                            batch = []
                            
                    except asyncio.TimeoutError:
                        if batch:
                            await self._process_validation_batch(validator, batch)
                            batch = []
                        continue
                        
                except Exception as e:
                    print(f"❌ 验证批次处理异常: {e}")
            
            # 处理剩余批次
            if batch:
                await self._process_validation_batch(validator, batch)
    
    async def _process_validation_batch(self, validator, urls: List[str]):
        """处理验证批次"""
        if not urls:
            return
        
        self.stats["current_activity"] = f"验证: {len(urls)} 个URL"
        
        try:
            result = await validator.batch_validate_urls(urls)
            
            # 更新状态
            for url in result["valid"]:
                self.state_manager.mark_url_validated(url, True)
            
            for url in result["invalid"]:
                self.state_manager.mark_url_validated(url, False)
            
            self.stats["urls_validated"] += len(urls)
            
            # 回调通知
            if self.callbacks["validation_complete"]:
                self.callbacks["validation_complete"](result)
            
            print(f"✅ 验证完成: {len(result['valid'])} 有效, {len(result['invalid'])} 无效")
            
        except Exception as e:
            print(f"❌ 批量验证失败: {e}")
    
    def _run_monitor_worker(self):
        """监控工作线程"""
        last_save = time.time()
        last_report = time.time()
        
        while self.is_running and not self.stop_event.is_set():
            try:
                current_time = time.time()
                
                # 定期保存状态
                if current_time - last_save > self.config["save_interval"]:
                    self.state_manager.save_state()
                    self.deduplicator.save_state()
                    last_save = current_time
                
                # 定期报告进度
                if current_time - last_report > self.config["progress_report_interval"]:
                    if self.callbacks["progress"]:
                        self.callbacks["progress"](self.get_status())
                    last_report = current_time
                
                time.sleep(5)
                
            except Exception as e:
                print(f"❌ 监控线程异常: {e}")
        
        # 最终保存
        self.state_manager.save_state()
        self.deduplicator.save_state()
        print("🔚 监控线程结束")
    
    def pause(self):
        """暂停收集"""
        self.is_paused = True
        print("⏸️  收集已暂停")
    
    def resume(self):
        """恢复收集"""
        self.is_paused = False
        print("▶️  收集已恢复")
    
    def stop(self):
        """停止收集"""
        print("🛑 正在停止收集...")
        self.is_running = False
        self.stop_event.set()
        
        # 等待线程结束
        for thread in [self.collection_thread, self.validation_thread, self.monitor_thread]:
            if thread and thread.is_alive():
                thread.join(timeout=10)
        
        print("✅ 收集已停止")
    
    def get_status(self) -> Dict[str, Any]:
        """获取当前状态"""
        runtime = time.time() - self.stats["start_time"] if self.stats["start_time"] > 0 else 0
        
        progress = (
            (self.stats["searches_completed"] / self.stats["total_searches_planned"] * 100)
            if self.stats["total_searches_planned"] > 0 else 0
        )
        
        return {
            "session_name": self.session_name,
            "is_running": self.is_running,
            "is_paused": self.is_paused,
            "current_activity": self.stats["current_activity"],
            "progress": progress,
            "searches_completed": self.stats["searches_completed"],
            "total_searches_planned": self.stats["total_searches_planned"],
            "urls_collected": self.stats["urls_collected"],
            "urls_validated": self.stats["urls_validated"],
            "runtime": runtime,
            "validation_queue_size": self.validation_queue.qsize() if hasattr(self.validation_queue, 'qsize') else 0
        }
    
    def print_status(self):
        """打印状态信息"""
        status = self.get_status()
        
        print(f"\n🎯 智能控制器状态")
        print(f"=" * 60)
        print(f"📊 会话名称: {status['session_name']}")
        print(f"🔄 运行状态: {'运行中' if status['is_running'] else '已停止'}")
        if status['is_paused']:
            print(f"⏸️  暂停状态: 已暂停")
        print(f"🎯 当前活动: {status['current_activity']}")
        print(f"📈 搜索进度: {status['searches_completed']}/{status['total_searches_planned']} ({status['progress']:.1f}%)")
        print(f"🔗 收集URL: {status['urls_collected']} 个")
        print(f"✅ 验证URL: {status['urls_validated']} 个")
        print(f"⏱️  运行时间: {status['runtime']:.1f}s")
        print(f"=" * 60)
    
    def cleanup(self):
        """清理资源"""
        self.stop()
        self.state_manager.cleanup()
        print("✅ 智能控制器清理完成")
