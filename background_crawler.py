#!/usr/bin/env python3
"""
BOSS直聘后台爬虫主程序
支持断点续传、实时验证、去重和完全后台运行
"""

import asyncio
import sys
import os
import time
import json
from datetime import datetime
from typing import Dict, List, Any

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from url_collection.smart_controller import SmartController
from url_collection.enhanced_state_manager import EnhancedStateManager
from url_collection.enhanced_url_validator import EnhancedURLValidator
from url_collection.deduplicator import Deduplicator

class BackgroundCrawler:
    """后台爬虫主控制器"""
    
    def __init__(self):
        self.controller = None
        self.session_name = None
        self.is_running = False
        
        # 配置
        self.config = {
            "collection_type": "comprehensive",  # comprehensive, keywords, quick
            "enable_validation": True,
            "enable_deduplication": True,
            "max_concurrent_searches": 3,
            "max_pages_per_search": 20,
            "output_formats": ["json", "txt"],
            "auto_save_interval": 30,
            "progress_report_interval": 10
        }
    
    def print_banner(self):
        """打印程序横幅"""
        print("🚀 BOSS直聘后台爬虫系统 v2.0")
        print("=" * 60)
        print("✅ 支持断点续传")
        print("✅ 实时URL验证")
        print("✅ 智能去重")
        print("✅ 完全后台运行")
        print("✅ 不影响用户操作")
        print("=" * 60)
    
    def print_menu(self):
        """打印主菜单"""
        print("\n📋 操作菜单:")
        print("1. 开始全面收集")
        print("2. 关键词收集")
        print("3. 快速收集")
        print("4. 查看状态")
        print("5. 暂停/恢复")
        print("6. 停止收集")
        print("7. 查看统计")
        print("8. 配置设置")
        print("9. 清理数据")
        print("0. 退出程序")
        print("-" * 40)
    
    async def start_comprehensive_collection(self):
        """开始全面收集"""
        print("🚀 启动全面收集模式...")
        
        session_name = f"comprehensive_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.controller = SmartController(session_name)
        self.session_name = session_name
        
        # 设置配置
        self.controller.set_config(
            max_concurrent_searches=self.config["max_concurrent_searches"],
            max_pages_per_search=self.config["max_pages_per_search"],
            enable_real_time_validation=self.config["enable_validation"],
            enable_deduplication=self.config["enable_deduplication"],
            headless_mode=True
        )
        
        # 设置回调
        self.controller.set_callback("progress", self._on_progress)
        self.controller.set_callback("url_found", self._on_url_found)
        self.controller.set_callback("validation_complete", self._on_validation_complete)
        self.controller.set_callback("error", self._on_error)
        
        # 开始收集
        result = await self.controller.start_collection("comprehensive")
        print(result)
        
        if "✅" in result:
            self.is_running = True
            print("📊 收集已在后台启动，您可以继续其他操作")
            print("💡 使用菜单选项 4 查看实时状态")
    
    async def start_keyword_collection(self):
        """开始关键词收集"""
        print("🔍 关键词收集模式")
        
        # 获取用户输入
        keywords_input = input("请输入关键词（用逗号分隔，默认: Python,Java,前端）: ").strip()
        keywords = [k.strip() for k in keywords_input.split(",")] if keywords_input else ["Python", "Java", "前端"]
        
        cities_input = input("请输入城市（用逗号分隔，默认: 北京,上海）: ").strip()
        cities = [c.strip() for c in cities_input.split(",")] if cities_input else ["北京", "上海"]
        
        print(f"🎯 关键词: {keywords}")
        print(f"🏙️  城市: {cities}")
        
        session_name = f"keywords_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.controller = SmartController(session_name)
        self.session_name = session_name
        
        # 设置配置
        self.controller.set_config(
            max_concurrent_searches=self.config["max_concurrent_searches"],
            max_pages_per_search=self.config["max_pages_per_search"],
            enable_real_time_validation=self.config["enable_validation"],
            enable_deduplication=self.config["enable_deduplication"],
            headless_mode=True
        )
        
        # 设置回调
        self.controller.set_callback("progress", self._on_progress)
        self.controller.set_callback("url_found", self._on_url_found)
        
        # 开始收集
        result = await self.controller.start_collection("keywords", keywords=keywords, cities=cities)
        print(result)
        
        if "✅" in result:
            self.is_running = True
    
    async def start_quick_collection(self):
        """开始快速收集"""
        print("⚡ 快速收集模式")
        
        max_urls_input = input("请输入目标URL数量（默认: 1000）: ").strip()
        max_urls = int(max_urls_input) if max_urls_input.isdigit() else 1000
        
        print(f"🎯 目标: {max_urls} 个URL")
        
        session_name = f"quick_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.controller = SmartController(session_name)
        self.session_name = session_name
        
        # 设置配置
        self.controller.set_config(
            max_concurrent_searches=self.config["max_concurrent_searches"],
            max_pages_per_search=10,  # 快速模式减少页数
            enable_real_time_validation=self.config["enable_validation"],
            enable_deduplication=self.config["enable_deduplication"],
            headless_mode=True
        )
        
        # 设置回调
        self.controller.set_callback("progress", self._on_progress)
        
        # 开始收集
        result = await self.controller.start_collection("quick", max_urls=max_urls)
        print(result)
        
        if "✅" in result:
            self.is_running = True
    
    def _on_progress(self, status: Dict[str, Any]):
        """进度回调"""
        if status.get("progress", 0) > 0:
            print(f"📊 进度更新: {status['progress']:.1f}% - {status.get('current_activity', '')}")
    
    def _on_url_found(self, urls: List[str], search_key: str):
        """URL发现回调"""
        print(f"🔗 发现URL: {len(urls)} 个 - {search_key}")
    
    def _on_validation_complete(self, result: Dict[str, Any]):
        """验证完成回调"""
        valid_count = len(result.get("valid", []))
        invalid_count = len(result.get("invalid", []))
        print(f"✅ 验证完成: {valid_count} 有效, {invalid_count} 无效")
    
    def _on_error(self, error: str):
        """错误回调"""
        print(f"❌ 错误: {error}")
    
    def show_status(self):
        """显示当前状态"""
        if not self.controller:
            print("❌ 没有活动的收集任务")
            return
        
        self.controller.print_status()
        
        # 显示详细统计
        if hasattr(self.controller, 'state_manager'):
            stats = self.controller.state_manager.get_statistics()
            print(f"\n📈 详细统计:")
            print(f"🔗 总URL数: {stats.get('total_urls', 0)}")
            print(f"✅ 有效URL: {stats.get('valid_urls', 0)}")
            print(f"📊 验证成功率: {stats.get('validation_success_rate', 0):.1f}%")
    
    def pause_resume(self):
        """暂停/恢复收集"""
        if not self.controller:
            print("❌ 没有活动的收集任务")
            return
        
        status = self.controller.get_status()
        if status.get("is_paused"):
            self.controller.resume()
        else:
            self.controller.pause()
    
    def stop_collection(self):
        """停止收集"""
        if not self.controller:
            print("❌ 没有活动的收集任务")
            return
        
        print("🛑 正在停止收集...")
        self.controller.stop()
        self.is_running = False
        
        # 保存最终结果
        self._save_final_results()
        print("✅ 收集已停止")
    
    def _save_final_results(self):
        """保存最终结果"""
        if not self.controller or not self.session_name:
            return
        
        try:
            # 获取所有收集的URL
            stats = self.controller.state_manager.get_statistics()
            
            # 保存到文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 保存JSON格式
            if "json" in self.config["output_formats"]:
                json_file = f"boss_urls_{self.session_name}_{timestamp}.json"
                with open(json_file, 'w', encoding='utf-8') as f:
                    json.dump({
                        "session_name": self.session_name,
                        "collection_time": timestamp,
                        "statistics": stats,
                        "urls": list(self.controller.state_manager.state["validated_urls"])
                    }, f, ensure_ascii=False, indent=2)
                print(f"📄 JSON文件已保存: {json_file}")
            
            # 保存TXT格式
            if "txt" in self.config["output_formats"]:
                txt_file = f"boss_urls_{self.session_name}_{timestamp}.txt"
                with open(txt_file, 'w', encoding='utf-8') as f:
                    for url in self.controller.state_manager.state["validated_urls"]:
                        f.write(f"{url}\n")
                print(f"📄 TXT文件已保存: {txt_file}")
            
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")
    
    def show_statistics(self):
        """显示统计信息"""
        if not self.controller:
            print("❌ 没有活动的收集任务")
            return
        
        # 显示控制器统计
        self.controller.print_status()
        
        # 显示去重统计
        if hasattr(self.controller, 'deduplicator'):
            self.controller.deduplicator.print_statistics()
    
    def configure_settings(self):
        """配置设置"""
        print("\n⚙️  当前配置:")
        for key, value in self.config.items():
            print(f"  {key}: {value}")
        
        print("\n可修改的配置项:")
        print("1. max_concurrent_searches (最大并发搜索数)")
        print("2. max_pages_per_search (每次搜索最大页数)")
        print("3. enable_validation (启用验证)")
        print("4. enable_deduplication (启用去重)")
        
        choice = input("请选择要修改的配置项 (1-4, 回车跳过): ").strip()
        
        if choice == "1":
            value = input(f"当前值: {self.config['max_concurrent_searches']}, 新值: ").strip()
            if value.isdigit():
                self.config["max_concurrent_searches"] = int(value)
                print("✅ 配置已更新")
        elif choice == "2":
            value = input(f"当前值: {self.config['max_pages_per_search']}, 新值: ").strip()
            if value.isdigit():
                self.config["max_pages_per_search"] = int(value)
                print("✅ 配置已更新")
        elif choice == "3":
            value = input(f"当前值: {self.config['enable_validation']}, 新值 (true/false): ").strip().lower()
            if value in ["true", "false"]:
                self.config["enable_validation"] = value == "true"
                print("✅ 配置已更新")
        elif choice == "4":
            value = input(f"当前值: {self.config['enable_deduplication']}, 新值 (true/false): ").strip().lower()
            if value in ["true", "false"]:
                self.config["enable_deduplication"] = value == "true"
                print("✅ 配置已更新")
    
    def cleanup_data(self):
        """清理数据"""
        print("🧹 数据清理选项:")
        print("1. 清理当前会话数据")
        print("2. 清理所有历史数据")
        print("3. 清理缓存文件")
        
        choice = input("请选择清理选项 (1-3): ").strip()
        
        if choice == "1" and self.controller:
            self.controller.state_manager.cleanup()
            self.controller.deduplicator.clear_all()
            print("✅ 当前会话数据已清理")
        elif choice == "2":
            # 清理所有相关文件
            import glob
            patterns = [
                "collection_state_*.json",
                "collection_state_*.db", 
                "collected_urls_*.txt",
                "bloom_filter_*.pkl",
                "exact_dedup_*.json",
                "dedup_stats_*.json",
                "url_validation_cache.json"
            ]
            
            for pattern in patterns:
                files = glob.glob(pattern)
                for file in files:
                    try:
                        os.remove(file)
                        print(f"🗑️  删除: {file}")
                    except:
                        pass
            print("✅ 历史数据已清理")
        elif choice == "3":
            cache_files = ["url_validation_cache.json"]
            for file in cache_files:
                try:
                    os.remove(file)
                    print(f"🗑️  删除缓存: {file}")
                except:
                    pass
            print("✅ 缓存文件已清理")
    
    async def run(self):
        """运行主程序"""
        self.print_banner()
        
        while True:
            try:
                self.print_menu()
                choice = input("请选择操作 (0-9): ").strip()
                
                if choice == "1":
                    await self.start_comprehensive_collection()
                elif choice == "2":
                    await self.start_keyword_collection()
                elif choice == "3":
                    await self.start_quick_collection()
                elif choice == "4":
                    self.show_status()
                elif choice == "5":
                    self.pause_resume()
                elif choice == "6":
                    self.stop_collection()
                elif choice == "7":
                    self.show_statistics()
                elif choice == "8":
                    self.configure_settings()
                elif choice == "9":
                    self.cleanup_data()
                elif choice == "0":
                    if self.is_running:
                        print("🛑 正在停止收集...")
                        self.stop_collection()
                    print("👋 再见！")
                    break
                else:
                    print("❌ 无效选择，请重试")
                
                # 短暂暂停
                await asyncio.sleep(0.5)
                
            except KeyboardInterrupt:
                print("\n🛑 接收到中断信号...")
                if self.is_running:
                    self.stop_collection()
                break
            except Exception as e:
                print(f"❌ 程序异常: {e}")
                continue

async def main():
    """主函数"""
    crawler = BackgroundCrawler()
    await crawler.run()

if __name__ == "__main__":
    asyncio.run(main())
